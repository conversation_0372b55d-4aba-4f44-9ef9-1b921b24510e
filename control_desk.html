<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>米家场景控制台</title>
    <style>
        :root {
            --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
            --bg-color: #1e1e1e;
            --sidebar-bg: #252526;
            --content-bg: #1e1e1e;
            --titlebar-bg: #323233;
            --statusbar-bg: #007acc;
            --list-hover-bg: #2a2d2e;
            --list-active-bg: #37373d;
            --border-color: #3c3c3c;
            --text-color: #cccccc;
            --text-color-darker: #888888;
            --accent-color: #0090f1;
            --btn-primary-bg: #0e639c;
            --btn-secondary-bg: #3a3d41;
            --error-color: #f48771;
            --success-color: #89d185;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background: #333;
            color: var(--text-color);
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* --- Main Layout --- */
        .container {
            width: 100%;
            max-width: 900px;
            height: 80vh;
            min-height: 600px;
            background: var(--content-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .title-bar {
            background: var(--titlebar-bg);
            padding: 8px 15px;
            font-weight: 500;
            flex-shrink: 0;
            text-align: center;
        }
        
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .sidebar {
            width: 240px;
            background: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            padding: 10px 0;
            flex-shrink: 0;
        }

        .sidebar h2 {
            font-size: 14px;
            padding: 10px 20px;
            color: var(--text-color-darker);
            text-transform: uppercase;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* --- Sidebar Home List --- */
        #home-list {
            list-style: none;
            flex-grow: 1;
        }
        .home-item {
            padding: 8px 20px;
            cursor: pointer;
            border-left: 2px solid transparent;
        }
        .home-item:hover {
            background: var(--list-hover-bg);
        }
        .home-item.active {
            background: var(--list-active-bg);
            border-left-color: var(--accent-color);
            color: white;
        }

        /* --- Main Content Area --- */
        .scene-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .scene-item {
            background: var(--sidebar-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 12px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: border-color 0.2s;
        }
        .scene-item:hover {
            border-color: var(--accent-color);
        }
        .scene-name {
            font-size: 15px;
            color: var(--text-color);
        }
        .scene-actions {
            display: flex;
            gap: 10px;
        }
        
        /* --- Login Section --- */
        #login-section {
            text-align: center;
            padding-top: 50px;
        }
        #login-section h2 {
            font-size: 1.2em;
            margin-bottom: 15px;
        }
        #login-section p {
             color: var(--text-color-darker);
             margin-bottom: 25px;
        }

        /* --- Status Bar --- */
        .status-bar {
            background: var(--statusbar-bg);
            color: white;
            padding: 5px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            font-size: 13px;
        }
        .status-left, .status-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        #refresh-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 2px 8px;
            border-radius: 4px;
        }
        #refresh-btn:hover {
            background: rgba(255, 255, 255, 0.15);
        }
        #refresh-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        /* --- Buttons & Messages --- */
        .btn {
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 6px 14px;
            font-size: 13px;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        .btn:hover { opacity: 0.9; }

        .btn-primary {
            background: var(--btn-primary-bg);
            border-color: var(--accent-color);
            color: white;
        }
        .btn-secondary {
            background: var(--btn-secondary-bg);
            color: var(--text-color);
        }
        
        .loading, .message {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-color-darker);
        }

        .message.error { color: var(--error-color); }
        .message.success { color: var(--success-color); }

        /* --- QR Code Styles --- */
        #qr-container {
            background: var(--sidebar-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        #qr-container h3 {
            color: var(--text-color);
            margin-bottom: 15px;
            font-size: 16px;
        }

        #qr-image {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: transform 0.2s ease;
        }

        #qr-image:hover {
            transform: scale(1.05);
        }

        #qr-container p {
            color: var(--text-color-darker);
            font-size: 13px;
        }

        /* --- Loading Animation --- */
        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 1s ease-in-out infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .qr-loading {
            text-align: center;
            padding: 20px;
        }

        .qr-loading p {
            margin-top: 10px;
            color: var(--text-color-darker);
        }

        /* Pop-up message */
        .popup-message {
            position: fixed;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            z-index: 1000;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        .popup-message.success { background-color: #1e824c; }
        .popup-message.error { background-color: #c0392b; }

    </style>
</head>

<body>
    <div class="container">
        <div class="title-bar">
            <h1>米家场景控制台</h1>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <h2>家庭</h2>
                <ul id="home-list">
                    <!-- 家庭列表将在这里动态生成 -->
                </ul>
            </div>

            <div class="content-area">
                <div id="scenes-container">
                    <!-- 场景列表或登录提示将在此处显示 -->
                </div>
            </div>
        </div>

        <div class="status-bar">
            <div class="status-left">
                <div id="status-indicator">正在初始化...</div>
                <div id="homes-count" style="display: none;"></div>
            </div>
            <div class="status-right">
                <button id="refresh-btn" onclick="refreshAll()" title="刷新所有数据">
                    <span id="refresh-icon">🔄</span>
                    <span id="refresh-text">刷新</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentHome = null;
        let authStatus = 'initializing';
        let homesData = [];

        document.addEventListener('DOMContentLoaded', function () {
            checkStatus();
        });

        // 检查服务状态
        async function checkStatus() {
            try {
                const response = await fetch('/status');
                const data = await response.json();
                authStatus = data.authentication_status;
                updateStatusDisplay(data);

                if (authStatus === 'ok') {
                    await loadHomes();
                } else {
                    displayLoginPrompt();
                }
            } catch (error) {
                console.error('检查状态失败:', error);
                updateStatusDisplay({ authentication_status: 'error' });
                displayMessage('无法连接到后端服务。', 'error');
            }
        }
        
        // 更新底部状态栏显示
        function updateStatusDisplay(data) {
            const statusIndicator = document.getElementById('status-indicator');
            const homesCount = document.getElementById('homes-count');

            switch (data.authentication_status) {
                case 'ok':
                    statusIndicator.textContent = '✅ 已连接';
                    homesCount.textContent = `发现 ${data.homes_count || 0} 个家庭`;
                    homesCount.style.display = 'block';
                    break;
                case 'need_login':
                    statusIndicator.textContent = '⚠️ 需要登录';
                    homesCount.style.display = 'none';
                    break;
                case 'logging_in':
                    statusIndicator.textContent = '🔄 登录中...';
                    homesCount.style.display = 'none';
                    break;
                case 'expired':
                    statusIndicator.textContent = '❌ 登录已过期';
                    homesCount.style.display = 'none';
                    break;
                case 'error':
                     statusIndicator.textContent = '❌ 连接失败';
                    homesCount.style.display = 'none';
                    break;
                default:
                    statusIndicator.textContent = '🔄 初始化中...';
                    homesCount.style.display = 'none';
            }
        }

        // 在主内容区显示登录提示
        function displayLoginPrompt() {
            const container = document.getElementById('scenes-container');
            container.innerHTML = `
                <div id="login-section">
                    <h2>需要登录米家账号</h2>
                    <p>点击下方按钮开始登录流程</p>
                    <button id="login-btn" class="btn btn-primary" onclick="startLogin()">开始登录</button>
                    <div id="login-status" style="margin-top: 20px;"></div>
                    <div id="qr-container" style="margin-top: 20px; text-align: center; display: none;">
                        <h3>请使用米家APP扫描二维码</h3>
                        <div id="qr-loading" class="qr-loading" style="display: block;">
                            <div class="loading-spinner"></div>
                            <p>正在生成二维码，请稍候...</p>
                        </div>
                        <img id="qr-image" src="" alt="二维码" style="max-width: 300px; border: 1px solid #ddd; padding: 10px; display: none;">
                        <p id="qr-instruction" style="margin-top: 10px; color: #666; display: none;">扫码完成后页面将自动刷新</p>
                    </div>
                </div>`;
        }

        // 开始登录
        async function startLogin() {
            const loginBtn = document.getElementById('login-btn');
            const loginStatus = document.getElementById('login-status');
            const qrContainer = document.getElementById('qr-container');
            const qrImage = document.getElementById('qr-image');

            if(!loginBtn) return;

            loginBtn.disabled = true;
            loginBtn.textContent = '正在生成二维码...';

            try {
                const response = await fetch('/login/start', { method: 'POST' });
                const data = await response.json();
                if (data.status === 'started') {
                    loginStatus.innerHTML = `<div class="message success">${data.message}</div>`;
                    authStatus = 'logging_in';
                    updateStatusDisplay({authentication_status: authStatus});

                    // 显示二维码
                    showQRCode();

                    // 开始监控登录状态
                    startLoginStatusMonitoring();
                } else {
                    loginStatus.innerHTML = `<div class="message error">${data.message}</div>`;
                }
            } catch (error) {
                loginStatus.innerHTML = `<div class="message error">启动登录失败: ${error.message}</div>`;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '开始登录';
            }
        }

        // 显示二维码
        function showQRCode() {
            const qrContainer = document.getElementById('qr-container');

            if (qrContainer) {
                qrContainer.style.display = 'block';

                // 开始轮询检查二维码是否生成
                checkQRCodeGeneration();
            }
        }

        // 检查二维码生成状态
        function checkQRCodeGeneration() {
            const qrLoading = document.getElementById('qr-loading');
            const qrImage = document.getElementById('qr-image');
            const qrInstruction = document.getElementById('qr-instruction');

            const checkInterval = setInterval(async () => {
                try {
                    const response = await fetch('/qr/status');
                    const status = await response.json();

                    if (status.exists) {
                        // 二维码已生成，显示图片
                        clearInterval(checkInterval);

                        if (qrLoading) qrLoading.style.display = 'none';
                        if (qrImage) {
                            qrImage.src = `/qr.png?t=${Date.now()}`;
                            qrImage.style.display = 'block';
                        }
                        if (qrInstruction) qrInstruction.style.display = 'block';

                        // 监听图片加载失败，重试加载
                        qrImage.onerror = function() {
                            setTimeout(() => {
                                qrImage.src = `/qr.png?t=${Date.now()}`;
                            }, 1000);
                        };
                    }
                } catch (error) {
                    console.error('检查二维码状态失败:', error);
                }
            }, 1000); // 每秒检查一次

            // 设置超时，避免无限等待
            setTimeout(() => {
                clearInterval(checkInterval);
                if (qrLoading && qrLoading.style.display !== 'none') {
                    showLoginError('二维码生成超时，请重试');
                }
            }, 30000); // 30秒超时
        }

        // 监控登录状态
        function startLoginStatusMonitoring() {
            const checkInterval = setInterval(async () => {
                try {
                    const response = await fetch('/status');
                    const status = await response.json();

                    if (status.authentication_status === 'ok') {
                        // 登录成功
                        clearInterval(checkInterval);
                        showLoginSuccess();
                        // 延迟刷新页面以显示成功消息
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else if (status.authentication_status === 'need_login') {
                        // 登录失败或超时
                        clearInterval(checkInterval);
                        showLoginError('登录失败或超时，请重试');
                    }
                } catch (error) {
                    console.error('检查登录状态失败:', error);
                }
            }, 2000); // 每2秒检查一次

            // 设置超时，避免无限等待
            setTimeout(() => {
                clearInterval(checkInterval);
                if (authStatus === 'logging_in') {
                    showLoginError('登录超时，请重试');
                }
            }, 300000); // 5分钟超时
        }

        // 显示登录成功
        function showLoginSuccess() {
            const loginStatus = document.getElementById('login-status');
            const qrContainer = document.getElementById('qr-container');

            if (loginStatus) {
                loginStatus.innerHTML = `<div class="message success">登录成功！页面即将刷新...</div>`;
            }
            if (qrContainer) {
                qrContainer.style.display = 'none';
            }
        }

        // 显示登录错误
        function showLoginError(message) {
            const loginStatus = document.getElementById('login-status');
            const qrContainer = document.getElementById('qr-container');
            const loginBtn = document.getElementById('login-btn');

            if (loginStatus) {
                loginStatus.innerHTML = `<div class="message error">${message}</div>`;
            }
            if (qrContainer) {
                qrContainer.style.display = 'none';
            }
            if (loginBtn) {
                loginBtn.disabled = false;
                loginBtn.textContent = '开始登录';
            }
            authStatus = 'need_login';
        }

        // 加载家庭列表到侧边栏
        async function loadHomes() {
            try {
                const response = await fetch('/homes');
                homesData = await response.json();
                const list = document.getElementById('home-list');
                list.innerHTML = '';

                if (Array.isArray(homesData) && homesData.length > 0) {
                    homesData.forEach((home, index) => {
                        const listItem = document.createElement('li');
                        listItem.className = 'home-item';
                        listItem.textContent = home.name;
                        listItem.dataset.homeName = home.name;
                        listItem.addEventListener('click', () => switchHome(home.name));
                        list.appendChild(listItem);
                    });
                    
                    // 默认选中第一个家庭
                    switchHome(homesData[0].name);

                } else {
                     displayMessage('未找到任何家庭。', 'message');
                }
            } catch (error) {
                console.error('加载家庭列表失败:', error);
                displayMessage(`加载家庭列表失败: ${error.message}`, 'error');
            }
        }

        // 切换家庭
        function switchHome(homeName) {
            currentHome = homeName;
            
            // 更新侧边栏高亮状态
            document.querySelectorAll('.home-item').forEach(item => {
                item.classList.toggle('active', item.dataset.homeName === homeName);
            });

            loadScenes(homeName);
        }

        // 加载场景列表
        async function loadScenes(homeName) {
            const container = document.getElementById('scenes-container');
            container.innerHTML = '<div class="loading">正在加载场景...</div>';

            try {
                const response = await fetch(`/scenes?home_name=${encodeURIComponent(homeName)}`);
                const scenes = await response.json();

                if (Array.isArray(scenes)) {
                    displayScenes(scenes);
                } else {
                    displayMessage('加载场景失败: ' + (scenes.message || '未知错误'), 'error');
                }
            } catch (error) {
                displayMessage('加载场景失败: ' + error.message, 'error');
            }
        }
        
        // 在主内容区显示信息
        function displayMessage(text, type = 'message') {
             const container = document.getElementById('scenes-container');
             container.innerHTML = `<div class="message ${type}">${text}</div>`;
        }

        // 显示场景列表
        function displayScenes(scenes) {
            const container = document.getElementById('scenes-container');
            container.innerHTML = '';

            if (scenes.length === 0) {
                displayMessage('当前家庭没有可用的场景。');
                return;
            }

            const list = document.createElement('div');
            list.className = 'scene-list';

            scenes.forEach(scene => {
                const item = document.createElement('div');
                item.className = 'scene-item';

                // 使用 innerHTML 来轻松创建内部结构
                item.innerHTML = `
                    <div class="scene-name">${scene.name}</div>
                    <div class="scene-actions">
                        <button class="btn btn-primary" onclick="runScene('${scene.name}')">执行</button>
                        <button class="btn btn-secondary" onclick="copyUrl('${scene.url}')">复制URL</button>
                    </div>
                `;
                list.appendChild(item);
            });
            container.appendChild(list);
        }

        // 执行场景
        async function runScene(sceneName) {
            try {
                const response = await fetch(`/run_scene/${encodeURIComponent(sceneName)}?home_name=${encodeURIComponent(currentHome)}`);
                const data = await response.json();
                if (data.status === 'success') {
                    showPopupMessage(`场景执行成功: ${sceneName}`, 'success');
                } else {
                    showPopupMessage(`场景执行失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showPopupMessage(`场景执行失败: ${error.message}`, 'error');
            }
        }

        // 复制URL
        function copyUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                showPopupMessage('URL已复制到剪贴板', 'success');
            }).catch(err => {
                showPopupMessage(`复制失败: ${err.message}`, 'error');
            });
        }

        // 显示弹窗消息
        function showPopupMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `popup-message ${type}`;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);
            setTimeout(() => {
                messageDiv.style.transition = 'opacity 0.5s';
                messageDiv.style.opacity = '0';
                setTimeout(() => messageDiv.remove(), 500);
            }, 3000);
        }

        // 手动刷新所有数据
        async function refreshAll() {
            const refreshBtn = document.getElementById('refresh-btn');
            const refreshIcon = document.getElementById('refresh-icon');
            const refreshText = document.getElementById('refresh-text');

            refreshBtn.disabled = true;
            refreshIcon.style.animation = 'spin 1s linear infinite';
            refreshText.textContent = '刷新中';

            try {
                await checkStatus();
                showPopupMessage('数据已刷新', 'success');
            } catch (error) {
                showPopupMessage(`刷新失败: ${error.message}`, 'error');
            } finally {
                refreshBtn.disabled = false;
                refreshIcon.style.animation = '';
                refreshText.textContent = '刷新';
            }
        }
        
        // 添加旋转动画的 keyframes
        const styleSheet = document.createElement("style");
        styleSheet.type = "text/css";
        styleSheet.innerText = `@keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }`;
        document.head.appendChild(styleSheet);
    </script>
</body>

</html>