{"userId": *********, "ssecurity": "gStp3UOdFOifAbhHgdZPTA==", "deviceId": "Q9rC3nUqmAalE5NW", "serviceToken": "99hbCjl1HMhISSvw8DeUdeRhGpUpRXKNOggO4/gfofH/iXLLO2mZUYcrBFPcoJHkjW45vpGLQlNfiq9nmhoJbvwe8loc11p1VTxCjPij0TkHOakGznlUSrFmaWgGyFn/EI2QXfla9iBLxaW92x2FY041YtPphBAy91DlLSob/jPVZ/3/5g5H298RLZCbZV0+", "cUserId": "J2df71NwQIIJ7HQ20Yc8nOT5Yzg", "expireTime": "2025-08-28 21:39:52", "account_info": {"userId": *********, "nickName": "o_。怪怪", "gender": "m", "icon": "https://cdn.cnbj1.fds.api.mi-img.com/user-avatar/ac0ca01b-88d7-4280-9775-36465e17f625.jpg", "account": "+86 176****3217", "safePhone": "+86 176****3217", "safePhoneAddressKey": "E0E694903D5D2A66", "hasBindSafePhone": true, "phoneModifyTime": *************, "safeEmail": "jen***g@f*****l.com", "emailModifyTime": *************, "hasBindSafeEmail": true, "hasSetPwd": false, "pwdUpdateTime": 0, "hasSetMibao": false, "profileBlocked": false, "snsBindInfo": {}, "openAppInfo": [], "region": "CN", "twoFactorAuth": 0, "securityLevel": 0, "countryListOnlyCN": false, "showBindSafePhone": false, "showMibao": false}}