# 米家场景 Web API 服务

这是一个轻量、稳定、且支持自动恢复的后台服务，用于将你的米家（Xiaomi Home）智能场景通过简单的 HTTP GET 请求暴露为网络接口。

你可以在任何支持HTTP请求的工具（如快捷指令、Tasker、浏览器、脚本）中，通过访问一个简单的URL来触发米家场景，实现智能家居的自动化和快捷控制。

## 核心特性

*   **稳定运行**: 通过后台线程主动进行健康检查，确保服务与米家服务器的连接状态。
*   **自动恢复**: 当登录凭证（Token）过期后，你只需重新运行登录脚本，服务便会自动检测并恢复，无需重启。
*   **使用简单**: 将复杂的场景ID封装，直接通过人类可读的**场景名称**进行调用。
*   **配置灵活**: 所有关键配置（服务地址、家庭名称等）均在 `config.ini` 文件中设置，清晰明了。
*   **状态监控**: 提供 `/status` 接口，可以随时查看服务的运行状态和认证情况。

## 文件结构

```
.
├── 后台服务.py       # 主服务程序，需要一直后台运行
├── 登录.py           # 首次运行时用于扫码登录，生成凭证
├── config.ini        # 配置文件
├── mijiaAPI.py       # （依赖）米家API的核心库
├── jsons/            # 自动生成的目录
│   └── auth.json     # 存放登录凭证，由 登录.py 生成
└── README.md         # 本说明文档
```

## 使用步骤

### 第1步：安装依赖

本项目需要 `Flask` `mijiaAPI` 和 `requests` 库。请通过 pip 安装：

```bash
pip install Flask mijiaAPI requests
```

### 第2步：修改配置文件

打开 `config.ini` 文件，根据你的实际情况进行修改。

```ini
[server]
# 服务监听的IP地址。'0.0.0.0' 表示局域网内所有设备都可以访问。
host = 0.0.0.0
# 服务监听的端口号。
port = 5000

[mijia]
# 你的米家 APP 中的“家庭”名称，例如 "我的家"。
# 这个名称必须与 APP 中的完全一致！
home_name = 我的家
# 后台检查登录状态的间隔时间（单位：秒）。3600 代表每小时检查一次。
check_interval_seconds = 3600
```

### 第3步：获取登录凭证

首次使用时，需要运行 `登录.py` 脚本来获取米家服务器的访问凭证。

```bash
python 登录.py
```
运行后，终端会显示一个二维码。请打开**米家APP**（不是微信）进行扫码，并确认登录。成功后，脚本会自动在 `jsons/` 目录下生成一个 `auth.json` 文件。

### 第4步：启动后台服务

现在，可以启动主服务了。

```bash
python 后台服务.py
```
服务启动后会一直保持运行。你可以使用 `nohup` 或 `screen` 等工具使其在后台持续运行。
```bash
# 示例：使用 nohup 在后台运行
nohup python -u 后台服务.py > service.log 2>&1 &
```

### 第5步：验证服务

服务启动后，可以通过浏览器访问以下地址来验证是否成功：
*   **查看服务状态**: `http://<你的IP地址>:5000/status`
*   **查看可用场景**: `http://<你的IP地址>:5000/scenes`

如果一切正常，`/scenes` 接口会返回一个包含你在指定家庭中所有场景名称的列表。

## API 接口文档

#### 1. 执行场景

这是最核心的接口，用于触发一个指定的智能场景。

*   **方法**: `GET`
*   **URL**: `/run_scene/<scene_name>`
*   **URL参数**:
    *   `scene_name` (必填): 你想要执行的场景的**完整名称**。如果名称中包含空格，请进行URL编码（例如将 `模式 A` 编码为 `模式%20A`）。

*   **调用示例**:
    假设你的服务IP是 `127.0.0.1`，要执行名为“回家”的场景：
    ```
    http://127.0.0.1:5000/run_scene/回家
    ```

*   **成功响应 (200 OK)**:
    ```json
    {
        "status": "success",
        "message": "场景 '回家' 在家庭 '我的家' 中已成功执行。",
        "result": { ... } // 米家服务器返回的原始结果
    }
    ```

*   **失败响应 (404 Not Found)**:
    ```json
    {
        "status": "error",
        "message": "未找到场景：无法在家庭 '我的家' 中找到名为 '回家' 的场景。"
    }
    ```

#### 2. 获取可用场景列表

获取在 `config.ini` 中配置的默认家庭下的所有场景名称。

*   **方法**: `GET`
*   **URL**: `/scenes`
*   **调用示例**: `http://127.0.0.1:5000/scenes`

*   **成功响应 (200 OK)**:
    ```json
    [
        "回家",
        "离家",
        "晚安模式",
        "影院模式"
    ]
    ```

#### 3. 查看服务状态

监控服务的健康状况和认证状态。

*   **方法**: `GET`
*   **URL**: `/status`
*   **调用示例**: `http://127.0.0.1:5000/status`

*   **成功响应 (200 OK)**:
    ```json
    {
        "service_status": "running",
        "authentication_status": "ok", // "ok", "expired", 或 "initializing"
        "default_home_name": "我的家",
        "last_check_time": "2023-10-27T10:30:00Z"
    }
    ```

#### 4. 手动刷新缓存

当你在米家APP中新增、删除或重命名了场景后，可以调用此接口强制刷新服务的场景缓存，而无需重启服务。

*   **方法**: `POST`
*   **URL**: `/refresh`
*   **调用示例 (使用curl)**: `curl -X POST http://127.0.0.1:5000/refresh`

*   **成功响应 (200 OK)**:
    ```json
    {
        "status": "success",
        "message": "缓存已成功刷新。"
    }
    ```

## 常见问题 (FAQ)

*   **问：服务状态显示 `authentication_status: "expired"` 怎么办？**
    答：这是因为登录凭证已过期。你只需要重新运行 `python 登录.py`，扫码登录生成新的 `auth.json` 文件即可。后台服务会自动检测到文件更新并尝试重新连接，**无需重启服务**。

*   **问：我在米家APP里改了场景名字，或者新增了一个场景，但是调用没反应？**
    答：服务启动时会将场景列表缓存到内存中以提高效率。如果你修改了场景，请调用 `/refresh` 接口来手动同步最新的场景列表。

*   **问：调用时返回 `404 Not Found`，提示找不到场景？**
    答：请按以下步骤排查：
    1.  确认你的场景名称与URL中输入的完全一致，包括大小写和空格。
    2.  访问 `/scenes` 接口，检查你的场景名称是否出现在返回的列表中。
    3.  检查 `config.ini` 中的 `home_name` 是否与你米家APP中的家庭名称完全一致。
    4.  如果以上都正确，尝试调用 `/refresh` 接口后再试。